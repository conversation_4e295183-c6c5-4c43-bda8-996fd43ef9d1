<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تبدیل کامنت‌های فایل bot.php</title>
    <style>
        body {
            font-family: '<PERSON><PERSON><PERSON>', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
        }
        .content {
            padding: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #333;
        }
        input[type="text"], textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
            font-family: inherit;
            transition: border-color 0.3s;
        }
        input[type="text"]:focus, textarea:focus {
            outline: none;
            border-color: #4CAF50;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        .btn {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: transform 0.2s;
        }
        .btn:hover {
            transform: translateY(-2px);
        }
        .result {
            margin-top: 20px;
            padding: 20px;
            border-radius: 8px;
            font-weight: bold;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #e2f3ff;
            color: #0c5460;
            border: 1px solid #b8daff;
            margin-bottom: 20px;
        }
        .file-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 تبدیل کامنت‌های فایل bot.php</h1>
            <p>تمام کامنت‌های فایل bot.php را به یک متن ثابت تبدیل کنید</p>
        </div>

        <div class="content">
            <?php
            $botFile = 'bot.php';
            $message = '';
            $messageType = '';

            // بررسی وجود فایل bot.php
            if (!file_exists($botFile)) {
                $message = "❌ فایل bot.php یافت نشد! لطفا مطمئن شوید که فایل در همین پوشه قرار دارد.";
                $messageType = 'error';
            }

            // پردازش فرم
            if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['replace_comments'])) {
                $newComment = trim($_POST['new_comment']);

                if (empty($newComment)) {
                    $message = "❌ لطفا متن جدید کامنت را وارد کنید.";
                    $messageType = 'error';
                } else {
                    // خواندن محتوای فایل
                    $content = file_get_contents($botFile);

                    if ($content === false) {
                        $message = "❌ خطا در خواندن فایل bot.php";
                        $messageType = 'error';
                    } else {
                        // شمارش کامنت‌های موجود
                        $commentPattern = '/لینک چسبون[^\r\n]*/u';
                        $matches = [];
                        preg_match_all($commentPattern, $content, $matches);
                        $commentCount = count($matches[0]);

                        if ($commentCount === 0) {
                            $message = "⚠️ هیچ کامنت 'لینک چسبون' در فایل یافت نشد.";
                            $messageType = 'error';
                        } else {
                            // جایگزینی تمام کامنت‌ها
                            $newContent = preg_replace($commentPattern, $newComment, $content);

                            // ایجاد فایل پشتیبان
                            $backupFile = 'bot_backup_' . date('Y-m-d_H-i-s') . '.php';
                            if (copy($botFile, $backupFile)) {
                                // ذخیره فایل جدید
                                if (file_put_contents($botFile, $newContent) !== false) {
                                    $message = "✅ موفقیت! $commentCount کامنت با موفقیت تبدیل شد.<br>";
                                    $message .= "📁 فایل پشتیبان: $backupFile";
                                    $messageType = 'success';
                                } else {
                                    $message = "❌ خطا در ذخیره فایل جدید.";
                                    $messageType = 'error';
                                }
                            } else {
                                $message = "❌ خطا در ایجاد فایل پشتیبان.";
                                $messageType = 'error';
                            }
                        }
                    }
                }
            }

            // نمایش اطلاعات فایل
            if (file_exists($botFile)) {
                $content = file_get_contents($botFile);
                $commentPattern = '/لینک چسبون[^\r\n]*/u';
                $matches = [];
                preg_match_all($commentPattern, $content, $matches);
                $commentCount = count($matches[0]);
                $fileSize = filesize($botFile);
                $lastModified = date('Y-m-d H:i:s', filemtime($botFile));

                echo '<div class="file-info">';
                echo '<h3>📊 اطلاعات فایل bot.php:</h3>';
                echo '<p><strong>حجم فایل:</strong> ' . number_format($fileSize) . ' بایت</p>';
                echo '<p><strong>آخرین تغییر:</strong> ' . $lastModified . '</p>';
                echo '<p><strong>تعداد کامنت‌های "لینک چسبون":</strong> ' . $commentCount . '</p>';

                if ($commentCount > 0) {
                    echo '<p><strong>نمونه کامنت‌های موجود:</strong></p>';
                    echo '<ul>';
                    $uniqueComments = array_unique($matches[0]);
                    $displayCount = 0;
                    foreach ($uniqueComments as $comment) {
                        if ($displayCount < 5) {
                            echo '<li><code>' . htmlspecialchars($comment) . '</code></li>';
                            $displayCount++;
                        }
                    }
                    if (count($uniqueComments) > 5) {
                        echo '<li>... و ' . (count($uniqueComments) - 5) . ' کامنت دیگر</li>';
                    }
                    echo '</ul>';
                }
                echo '</div>';
            }

            // نمایش پیام
            if (!empty($message)) {
                echo '<div class="result ' . $messageType . '">' . $message . '</div>';
            }
            ?>

            <div class="info">
                <h3>📋 راهنما:</h3>
                <ul>
                    <li>این ابزار تمام کامنت‌های "لینک چسبون" را در فایل bot.php پیدا می‌کند</li>
                    <li>آنها را با متن دلخواه شما جایگزین می‌کند</li>
                    <li>قبل از تغییر، یک فایل پشتیبان ایجاد می‌شود</li>
                    <li>متن جدید می‌تواند هر چیزی باشد (حتی خالی)</li>
                </ul>
            </div>

            <?php if (file_exists($botFile)): ?>
            <form method="POST">
                <div class="form-group">
                    <label for="new_comment">💬 متن جدید کامنت:</label>
                    <textarea
                        name="new_comment"
                        id="new_comment"
                        placeholder="متن جدید کامنت را اینجا وارد کنید... (می‌تواند خالی باشد)"
                        required
                    ><?php echo isset($_POST['new_comment']) ? htmlspecialchars($_POST['new_comment']) : '// کامنت جدید'; ?></textarea>
                    <small style="color: #666;">مثال: // کامنت جدید یا /* توضیحات */ یا حتی خالی</small>
                </div>

                <button type="submit" name="replace_comments" class="btn">
                    🔄 تبدیل تمام کامنت‌ها
                </button>
            </form>
            <?php endif; ?>

            <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; text-align: center; color: #666;">
                <p>⚠️ <strong>هشدار:</strong> قبل از استفاده، حتماً از فایل bot.php پشتیبان تهیه کنید!</p>
                <p>🔧 ساخته شده برای تبدیل کامنت‌های فایل bot.php</p>
            </div>
        </div>
    </div>
</body>
</html>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تبدیل کامنت‌های فایل bot.php</title>
    <style>
        body {
            font-family: 'Tahoma', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
        }
        .content {
            padding: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #333;
        }
        input[type="text"], textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
            font-family: inherit;
            transition: border-color 0.3s;
        }
        input[type="text"]:focus, textarea:focus {
            outline: none;
            border-color: #4CAF50;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        .btn {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: transform 0.2s;
        }
        .btn:hover {
            transform: translateY(-2px);
        }
        .result {
            margin-top: 20px;
            padding: 20px;
            border-radius: 8px;
            font-weight: bold;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #e2f3ff;
            color: #0c5460;
            border: 1px solid #b8daff;
            margin-bottom: 20px;
        }
        .file-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 تبدیل کامنت‌های فایل bot.php</h1>
            <p>تمام کامنت‌های فایل bot.php را به یک متن ثابت تبدیل کنید</p>
        </div>

        <div class="content">
            <?php
            $botFile = 'bot.php';
            $message = '';
            $messageType = '';

            // بررسی وجود فایل bot.php
            if (!file_exists($botFile)) {
                $message = "❌ فایل bot.php یافت نشد! لطفا مطمئن شوید که فایل در همین پوشه قرار دارد.";
                $messageType = 'error';
            }

            // پردازش فرم
            if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['replace_comments'])) {
                $newComment = trim($_POST['new_comment']);

                if (empty($newComment)) {
                    $message = "❌ لطفا متن جدید کامنت را وارد کنید.";
                    $messageType = 'error';
                } else {
                    // خواندن محتوای فایل
                    $content = file_get_contents($botFile);

                    if ($content === false) {
                        $message = "❌ خطا در خواندن فایل bot.php";
                        $messageType = 'error';
                    } else {
                        // شمارش کامنت‌های موجود
                        $commentPattern = '/لینک چسبون[^\r\n]*/u';
                        $matches = [];
                        preg_match_all($commentPattern, $content, $matches);
                        $commentCount = count($matches[0]);

                        if ($commentCount === 0) {
                            $message = "⚠️ هیچ کامنت 'لینک چسبون' در فایل یافت نشد.";
                            $messageType = 'error';
                        } else {
                            // جایگزینی تمام کامنت‌ها
                            $newContent = preg_replace($commentPattern, $newComment, $content);

                            // ایجاد فایل پشتیبان
                            $backupFile = 'bot_backup_' . date('Y-m-d_H-i-s') . '.php';
                            if (copy($botFile, $backupFile)) {
                                // ذخیره فایل جدید
                                if (file_put_contents($botFile, $newContent) !== false) {
                                    $message = "✅ موفقیت! $commentCount کامنت با موفقیت تبدیل شد.<br>";
                                    $message .= "📁 فایل پشتیبان: $backupFile";
                                    $messageType = 'success';
                                } else {
                                    $message = "❌ خطا در ذخیره فایل جدید.";
                                    $messageType = 'error';
                                }
                            } else {
                                $message = "❌ خطا در ایجاد فایل پشتیبان.";
                                $messageType = 'error';
                            }
                        }
                    }
                }
            }

            // نمایش اطلاعات فایل
            if (file_exists($botFile)) {
                $content = file_get_contents($botFile);
                $commentPattern = '/لینک چسبون[^\r\n]*/u';
                $matches = [];
                preg_match_all($commentPattern, $content, $matches);
                $commentCount = count($matches[0]);
                $fileSize = filesize($botFile);
                $lastModified = date('Y-m-d H:i:s', filemtime($botFile));
<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Comment Replacer - جایگزین کننده کامنت‌ها</title>
    <style>
        body {
            font-family: 'Tahoma', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(45deg, #2196F3, #21CBF3);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2em;
        }
        .content {
            padding: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #333;
        }
        input[type="text"], textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
        }
        input[type="text"]:focus, textarea:focus {
            outline: none;
            border-color: #2196F3;
        }
        .btn {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            transition: transform 0.2s;
            width: 100%;
        }
        .btn:hover {
            transform: translateY(-2px);
        }
        .result {
            margin-top: 30px;
            padding: 20px;
            border-radius: 8px;
            border-left: 5px solid #2196F3;
            background: #f8f9fa;
        }
        .success { border-left-color: #4CAF50; background: #e8f5e8; }
        .error { border-left-color: #f44336; background: #ffeaea; }
        .warning { border-left-color: #ff9800; background: #fff3e0; }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-box {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border: 2px solid #e9ecef;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #2196F3;
        }
        .checkbox-group {
            display: flex;
            align-items: center;
            margin: 10px 0;
        }
        .checkbox-group input[type="checkbox"] {
            margin-left: 10px;
            transform: scale(1.2);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 Comment Replacer</h1>
            <p>جایگزین کننده کامنت‌های فایل PHP</p>
        </div>
        <div class="content">
            <?php
            // تنظیمات پیش‌فرض
            $input_file = 'bot.php';
            $new_comment = '// Modified comment';

            if ($_SERVER['REQUEST_METHOD'] === 'POST') {
                $input_file = $_POST['input_file'] ?? 'bot.php';
                $new_comment = $_POST['new_comment'] ?? '// Modified comment';
                $preserve_phpdoc = isset($_POST['preserve_phpdoc']);
                $preserve_license = isset($_POST['preserve_license']);

                processFile($input_file, $new_comment, $preserve_phpdoc, $preserve_license);
            } else {
                showForm($input_file, $new_comment);
            }

            function showForm($input_file, $new_comment) {
                ?>
                <form method="POST">
                    <div class="form-group">
                        <label for="input_file">📁 نام فایل ورودی:</label>
                        <input type="text" id="input_file" name="input_file" value="<?= htmlspecialchars($input_file) ?>" required>
                    </div>

                    <div class="form-group">
                        <label for="new_comment">💬 کامنت جدید:</label>
                        <textarea id="new_comment" name="new_comment" rows="3" required><?= htmlspecialchars($new_comment) ?></textarea>
                    </div>

                    <div class="form-group">
                        <div class="checkbox-group">
                            <label for="preserve_phpdoc">حفظ کامنت‌های PHPDoc (/** ... */)</label>
                            <input type="checkbox" id="preserve_phpdoc" name="preserve_phpdoc" checked>
                        </div>
                        <div class="checkbox-group">
                            <label for="preserve_license">حفظ کامنت‌های مجوز و کپی‌رایت</label>
                            <input type="checkbox" id="preserve_license" name="preserve_license" checked>
                        </div>
                    </div>

                    <button type="submit" class="btn">🚀 شروع پردازش</button>
                </form>
                <?php
            }

            function processFile($input_file, $new_comment, $preserve_phpdoc, $preserve_license) {

// بررسی وجود فایل
if (!file_exists($input_file)) {
    die("❌ فایل $input_file یافت نشد!\n");
}

echo "🔄 شروع پردازش فایل $input_file...\n";

// ایجاد بک‌آپ
if (copy($input_file, $backup_file)) {
    echo "✅ بک‌آپ در $backup_file ایجاد شد\n";
} else {
    die("❌ خطا در ایجاد بک‌آپ!\n");
}

// خواندن محتوای فایل
$content = file_get_contents($input_file);
if ($content === false) {
    die("❌ خطا در خواندن فایل!\n");
}

echo "📊 اندازه فایل اصلی: " . strlen($content) . " بایت\n";

// شمارش کامنت‌های اصلی
$original_single_comments = preg_match_all('/\/\/.*$/m', $content);
$original_multi_comments = preg_match_all('/\/\*.*?\*\//s', $content);

echo "📝 کامنت‌های تک خطی یافت شده: $original_single_comments\n";
echo "📝 کامنت‌های چند خطی یافت شده: $original_multi_comments\n";

// پردازش هوشمند کامنت‌ها
$processed_content = $content;

// جایگزینی کامنت‌های تک خطی (// ...)
if ($preserve_license_comments) {
    // حفظ کامنت‌های حاوی کلمات کلیدی مجوز
    $license_keywords = ['copyright', 'license', 'author', '@license', '@copyright', '@author'];
    $processed_content = preg_replace_callback('/\/\/(.*)$/m', function($matches) use ($new_comment, $license_keywords) {
        $comment_text = strtolower($matches[1]);
        foreach ($license_keywords as $keyword) {
            if (strpos($comment_text, $keyword) !== false) {
                return $matches[0]; // حفظ کامنت اصلی
            }
        }
        return $new_comment;
    }, $processed_content);
} else {
    $processed_content = preg_replace('/\/\/.*$/m', $new_comment, $processed_content);
}

// جایگزینی کامنت‌های چند خطی (/* ... */)
if ($preserve_php_doc_comments) {
    // حفظ کامنت‌های PHPDoc که با /** شروع می‌شوند
    $processed_content = preg_replace_callback('/\/\*(?!\*).*?\*\//s', function($matches) use ($new_comment) {
        return $new_comment;
    }, $processed_content);
} else {
    $processed_content = preg_replace('/\/\*.*?\*\//s', $new_comment, $processed_content);
}

$content = $processed_content;

// شمارش کامنت‌های جدید برای تأیید
$new_single_comments = preg_match_all('/\/\/.*$/m', $content);
$new_multi_comments = preg_match_all('/\/\*.*?\*\//s', $content);

echo "✅ کامنت‌های تک خطی جایگزین شده: $new_single_comments\n";
echo "✅ کامنت‌های چند خطی جایگزین شده: $new_multi_comments\n";

// ذخیره فایل جدید
if (file_put_contents($output_file, $content) !== false) {
    echo "✅ فایل جدید در $output_file ذخیره شد\n";
    echo "📊 اندازه فایل جدید: " . strlen($content) . " بایت\n";
} else {
    die("❌ خطا در ذخیره فایل جدید!\n");
}

// نمایش آمار نهایی
echo "\n📈 گزارش نهایی:\n";
echo "- فایل اصلی: $input_file\n";
echo "- فایل بک‌آپ: $backup_file\n";
echo "- فایل جدید: $output_file\n";
echo "- کامنت جایگزین: '$new_comment'\n";
echo "- تعداد کل تغییرات: " . ($original_single_comments + $original_multi_comments) . "\n";

// سوال برای جایگزینی فایل اصلی
echo "\n❓ آیا می‌خواهید فایل اصلی را با فایل جدید جایگزین کنید؟ (y/n): ";
$handle = fopen("php://stdin", "r");
$line = fgets($handle);
fclose($handle);

if (trim(strtolower($line)) == 'y' || trim(strtolower($line)) == 'yes') {
    if (copy($output_file, $input_file)) {
        echo "✅ فایل اصلی جایگزین شد\n";
        unlink($output_file); // حذف فایل موقت
        echo "🗑️ فایل موقت حذف شد\n";
    } else {
        echo "❌ خطا در جایگزینی فایل اصلی!\n";
    }
} else {
    echo "ℹ️ فایل اصلی تغییر نکرد. فایل جدید در $output_file موجود است.\n";
}

echo "\n🎉 عملیات با موفقیت انجام شد!\n";

?>
